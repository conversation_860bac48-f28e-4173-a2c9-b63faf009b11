#!/usr/bin/env python3
"""
测试UI自动化工具类
"""

import sys
import os

# 添加当前目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_ui_utils():
    """测试UI工具类"""
    
    print("=" * 60)
    print("🧪 测试UI自动化工具类")
    print("=" * 60)
    
    try:
        # 测试导入
        from ui_automation_utils import UIAutomationUtils, find_and_click, find_element, wait_for_element, click_if_exists
        print("✅ 工具类导入成功")
        
        # 测试类方法是否存在
        methods_to_test = [
            'find_and_click',
            'find_element', 
            'wait_for_element',
            'click_if_exists',
            'sanitize_filename'
        ]
        
        for method_name in methods_to_test:
            if hasattr(UIAutomationUtils, method_name):
                print(f"✅ UIAutomationUtils.{method_name} - 方法存在")
            else:
                print(f"❌ UIAutomationUtils.{method_name} - 方法不存在")
        
        # 测试全局函数是否存在
        global_functions = [
            find_and_click,
            find_element,
            wait_for_element,
            click_if_exists
        ]
        
        for func in global_functions:
            if callable(func):
                print(f"✅ {func.__name__} - 全局函数可调用")
            else:
                print(f"❌ {func.__name__} - 全局函数不可调用")
        
        # 测试 sanitize_filename 方法
        test_filename = "test/file:name*with?illegal<chars>|.png"
        clean_filename = UIAutomationUtils.sanitize_filename(test_filename)
        print(f"📝 文件名清理测试:")
        print(f"   原始: {test_filename}")
        print(f"   清理后: {clean_filename}")
        
        print("\n🎉 所有测试通过！工具类功能正常")
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_integration():
    """测试与现有脚本的集成"""
    
    print("\n" + "=" * 60)
    print("🔗 测试与现有脚本的集成")
    print("=" * 60)
    
    try:
        # 测试天猫脚本导入
        import 天猫
        print("✅ 天猫脚本导入成功")
        
        # 检查中文方法是否存在
        chinese_methods = ['福气任务', '测试任务', '快手任务']
        for method_name in chinese_methods:
            if hasattr(天猫, method_name):
                print(f"✅ 天猫.{method_name} - 中文方法存在")
            else:
                print(f"❌ 天猫.{method_name} - 中文方法不存在")
        
        # 测试花园脚本导入
        import task_tianmao_huayuan
        print("✅ 花园脚本导入成功")
        
        # 检查中文方法是否存在
        chinese_methods = ['花园任务', '自动拖拽']
        for method_name in chinese_methods:
            if hasattr(task_tianmao_huayuan, method_name):
                print(f"✅ task_tianmao_huayuan.{method_name} - 中文方法存在")
            else:
                print(f"❌ task_tianmao_huayuan.{method_name} - 中文方法不存在")
        
        print("\n🎉 集成测试通过！脚本可以正常使用工具类")
        
    except ImportError as e:
        print(f"❌ 脚本导入失败: {e}")
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")

if __name__ == "__main__":
    test_ui_utils()
    test_integration()
